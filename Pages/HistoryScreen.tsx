import React, { useCallback, useState, useRef, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Alert, Animated, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Colors, Spacing, BorderRadius, Shadow } from '../theme';
import Screen from '../Components/Screen';
import Typography from '../Components/Typography';
import Card from '../Components/Card';
import GradeIndicator from '../Components/GradeIndicator';
import Checkbox from '../Components/Checkbox';
import CustomButton from '../Components/CustomButton';
import BackButton from '../Components/BackButton';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useHistory, useScan, useAuth, ScanResult } from '../context';

type RootStackParamList = {
  History: undefined;
  Home: undefined;
  Scan: { fromMainNav: boolean };
  ScanResults: {
    imageUri: string;
    isAnonymous: boolean;
    apiResponse: any;
    fromHistory?: boolean;
  };
  ComparisonSelection: { currentProductId?: string };
  ComparisonView: { selectedProductIds: string[] };
  Settings: undefined;
};

type HistoryScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'History'
>;

// We'll use the history context instead of mock data

const HistoryScreen: React.FC = () => {
  const navigation = useNavigation<HistoryScreenNavigationProp>();
  const { historyState, removeScanFromHistory, removeMultipleScansFromHistory } = useHistory();
  const { clearCurrentScan } = useScan();
  const { authState } = useAuth();
  
  // Animation values
  const selectionModeAnimation = useRef(new Animated.Value(0)).current;
  const actionButtonsAnimation = useRef(new Animated.Value(0)).current;
  const listItemAnimations = useRef<{[key: string]: Animated.Value}>({}).current;

  // State for selection mode
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Toggle selection mode
  const toggleSelectionMode = () => {
    if (isSelectionMode) {
      // Exit selection mode and clear selections
      exitSelectionMode();
      
      // Animate out
      Animated.timing(selectionModeAnimation, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
      
      Animated.timing(actionButtonsAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      // Enter selection mode
      setIsSelectionMode(true);
      
      // Animate in
      Animated.timing(selectionModeAnimation, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  };

  // Toggle item selection
  const toggleItemSelection = (id: string) => {
    setSelectedItems((prevSelected) => {
      const wasSelected = prevSelected.includes(id);
      const newSelection = wasSelected
        ? prevSelected.filter((itemId) => itemId !== id)
        : [...prevSelected, id];
      
      // Animate action buttons when first item is selected
      if (newSelection.length === 1 && prevSelected.length === 0) {
        Animated.timing(actionButtonsAnimation, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }).start();
      } else if (newSelection.length === 0 && prevSelected.length === 1) {
        Animated.timing(actionButtonsAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }
      
      // Animate the selected item
      if (!listItemAnimations[id]) {
        listItemAnimations[id] = new Animated.Value(0);
      }
      
      Animated.sequence([
        Animated.timing(listItemAnimations[id], {
          toValue: wasSelected ? 0 : 1.05,
          duration: 120,
          useNativeDriver: true,
        }),
        Animated.timing(listItemAnimations[id], {
          toValue: wasSelected ? 0 : 1,
          duration: 100,
          useNativeDriver: true,
        })
      ]).start();
      
      return newSelection;
    });
  };

  // Select all items
  const selectAllItems = () => {
    if (historyState.scans.length === 0) return;

    const allItemIds = historyState.scans.map(scan => scan.id);
    setSelectedItems(allItemIds);
  };

  // Check if all items are selected
  const isAllSelected = selectedItems.length === historyState.scans.length && historyState.scans.length > 0;

  // Clear selection when exiting selection mode
  const exitSelectionMode = () => {
    setIsSelectionMode(false);
    setSelectedItems([]);
  };

  // Handle compare button press
  const handleCompare = () => {
    if (selectedItems.length < 2) {
      Alert.alert(
        'Select More Items',
        'Please select at least 2 items to compare.',
        [{ text: 'OK', onPress: () => {} }]
      );
    } else if (selectedItems.length > 3) {
      Alert.alert(
        'Too Many Items Selected',
        'Please select up to 3 items for the best comparison experience.',
        [{ text: 'OK', onPress: () => {} }]
      );
    } else {
      navigation.navigate('ComparisonView', { selectedProductIds: selectedItems });
      // Exit selection mode after comparison
      exitSelectionMode();
    }
  };

  // Handle delete button press
  const handleDelete = () => {
    if (selectedItems.length === 0) {
      Alert.alert(
        'No Items Selected',
        'Please select items to delete.',
        [{ text: 'OK', onPress: () => {} }]
      );
      return;
    }

    Alert.alert(
      'Delete Selected Items',
      `Are you sure you want to delete ${selectedItems.length} selected item${selectedItems.length > 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Use bulk delete for better performance and reliability
              if (selectedItems.length === 1) {
                await removeScanFromHistory(selectedItems[0]);
              } else {
                await removeMultipleScansFromHistory(selectedItems);
              }
              // Exit selection mode after deletion
              exitSelectionMode();
            } catch (error) {
              console.error('Error deleting items:', error);
              Alert.alert('Error', 'Failed to delete selected items. Please try again.');
            }
          }
        }
      ]
    );
  };

  // Automatic duplicate handling is now done in HistoryContext

  const handleScan = async () => {
    // Clear the current scan before starting a new one
    await clearCurrentScan();
    navigation.navigate('Scan', { fromMainNav: true });
  };

  const handleItemPress = (item: ScanResult) => {
    // Navigate to ScanResults with the scan data
    navigation.navigate('ScanResults', {
      imageUri: item.imageUri,
      isAnonymous: false, // Required by type definition, but ScanResultsScreen will use authState
      fromHistory: true,  // Mark that we're navigating from history screen
      apiResponse: {
        rating: item.grade,
        summary: item.summary,
        product_details: {
          name: item.productName,
          ingredients: [...item.goodIngredients.map(i => i.name), ...item.badIngredients.map(i => i.name)]
        },
        good_ingredients: item.goodIngredients.map(i => ({
          name: i.name,
          description: i.description
        })),
        harmful_ingredient_analysis: item.badIngredients.map(i => ({
          ingredient: i.name,
          impact: i.description
        })),
        explanation: {
          influencing_ingredients: item.badIngredients.map(i => i.name),
          rationale: item.explanation
        },
        effects: item.effects || {
          positive_effects: { short_term: [], long_term: [] },
          negative_effects: { short_term: [], long_term: [] }
        },
        info_cards: item.infoCards || [],
        detailed_rationale: {
          overall_assessment: item.summary
        }
      }
    });
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };
  
  // Render empty state when no history is available
  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <Ionicons name="time-outline" size={64} color={Colors.AccentBlue} />
        </View>
        <Typography variant="heading2" style={styles.emptyTitle}>
          No Scan History Yet
        </Typography>
        <Typography variant="bodyText" style={styles.emptySubText}>
          Products you scan will appear here for easy access and comparison
        </Typography>
        <CustomButton
          title="Scan Your First Product"
          onPress={() => navigation.navigate('Scan', { fromMainNav: true })}
          style={styles.emptyCta}
          icon={<Ionicons name="scan-outline" size={20} color={Colors.BackgroundPrimary} style={{marginRight: 8}} />}
        />
      </View>
    );
  };

  // Initialize animations for new items
  useEffect(() => {
    if (historyState.scans) {
      historyState.scans.forEach((item) => {
        if (!listItemAnimations[item.id]) {
          listItemAnimations[item.id] = new Animated.Value(0);
        }
      });
    }
  }, [historyState.scans]);

  // Separate component for list items to properly use hooks
  const HistoryListItem: React.FC<{
    item: ScanResult;
    index: number;
    isSelected: boolean;
    isSelectionMode: boolean;
    onPress: () => void;
    onToggleSelection: () => void;
    listItemAnimations: { [key: string]: Animated.Value };
  }> = ({ item, index, isSelected, isSelectionMode, onPress, onToggleSelection, listItemAnimations }) => {
    // Get the first letter of the product name for the thumbnail
    const firstLetter = item.productName.charAt(0).toUpperCase();

    // Get color based on grade for the thumbnail background
    const getGradeColor = (grade: string) => {
      switch(grade.toUpperCase()) {
        case 'A': return Colors.GradeA;
        case 'B': return Colors.GradeB;
        case 'C': return Colors.GradeC;
        case 'D': return Colors.GradeD;
        case 'E': return Colors.GradeE;
        default: return Colors.LightText;
      }
    };

    const gradeColor = getGradeColor(item.grade);

    // Create entry animation for list items
    const entryAnimation = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      Animated.timing(entryAnimation, {
        toValue: 1,
        duration: 300,
        delay: index * 50, // Stagger the animations
        useNativeDriver: true,
      }).start();
    }, []);

    // Get or create item selection animation
    if (!listItemAnimations[item.id]) {
      listItemAnimations[item.id] = new Animated.Value(isSelected ? 1 : 0);
    }

    return (
      <Animated.View style={{
        opacity: entryAnimation,
        transform: [
          { translateY: entryAnimation.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0]
          })},
          { scale: listItemAnimations[item.id] ? listItemAnimations[item.id].interpolate({
            inputRange: [0, 1],
            outputRange: [1, 1.02]
          }) : 1 }
        ]
      }}>
        <TouchableOpacity
          onPress={onPress}
          activeOpacity={0.7}
          style={styles.itemWrapper}
        >
        <Card shadow="medium" padding="none" style={StyleSheet.flatten([styles.listItemCard, isSelected && styles.selectedCard])}>
          <View style={styles.itemRow}>
            <View style={StyleSheet.flatten([styles.itemThumbnail, { backgroundColor: `${gradeColor}15` }])}>
              <View style={StyleSheet.flatten([styles.gradeBadge, { backgroundColor: gradeColor }])}>
                <Typography variant="caption" style={styles.gradeText}>
                  {item.grade.toUpperCase()}
                </Typography>
              </View>
              <Typography variant="heading2" style={StyleSheet.flatten([styles.thumbnailText, { color: gradeColor }])}>
                {firstLetter}
              </Typography>
            </View>
            <View style={styles.itemContent}>
              <Typography variant="bodyText" style={styles.itemTitle}>
                {item.productName}
              </Typography>
              <View style={styles.itemMetaRow}>
                <Ionicons name="calendar-outline" size={14} color={Colors.LightText} style={styles.metaIcon} />
                <Typography variant="description" style={styles.itemSubtitle}>
                  {formatDate(item.timestamp)}
                </Typography>
              </View>
              {item.badIngredients && item.badIngredients.length > 0 && (
                <View style={styles.tagsContainer}>
                  {item.badIngredients.slice(0, 2).map((ingredient, index) => (
                    <View key={index} style={styles.tagItem}>
                      <Typography variant="caption" style={styles.tagText}>
                        {ingredient.name.length > 12 ? ingredient.name.substring(0, 10) + '...' : ingredient.name}
                      </Typography>
                    </View>
                  ))}
                  {item.badIngredients.length > 2 && (
                    <View style={styles.tagItem}>
                      <Typography variant="caption" style={styles.tagText}>
                        +{item.badIngredients.length - 2}
                      </Typography>
                    </View>
                  )}
                </View>
              )}
            </View>
            <View style={styles.itemRight}>
              {isSelectionMode ? (
                <Checkbox
                  checked={isSelected}
                  onPress={onToggleSelection}
                  size={24}
                />
              ) : (
                <Ionicons name="chevron-forward" size={20} color={Colors.LightText} />
              )}
            </View>
          </View>
        </Card>
      </TouchableOpacity>
      </Animated.View>
    );
  };

  const renderItem = ({ item, index }: { item: ScanResult; index: number }) => {
    const isSelected = selectedItems.includes(item.id);

    return (
      <HistoryListItem
        item={item}
        index={index}
        isSelected={isSelected}
        isSelectionMode={isSelectionMode}
        onPress={() => (isSelectionMode ? toggleItemSelection(item.id) : handleItemPress(item))}
        onToggleSelection={() => toggleItemSelection(item.id)}
        listItemAnimations={listItemAnimations}
      />
    );
  };
  return (
    <Screen style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.BackgroundPrimary} />
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <BackButton 
            onPress={() => navigation.navigate('Home')} 
            style={styles.backButton}
          />
          <Typography variant="heading1" style={styles.title}>History</Typography>
          {historyState.scans.length > 0 && (
            <TouchableOpacity 
              style={styles.selectButton} 
              onPress={toggleSelectionMode}
              activeOpacity={0.7}
            >
              {isSelectionMode ? (
                <MaterialIcons name="close" size={24} color={Colors.AccentBlue} />
              ) : (
                <MaterialIcons name="edit" size={22} color={Colors.DarkText} />
              )}
            </TouchableOpacity>
          )}
        </View>
        
        {historyState.scans.length > 0 && (
          <Animated.View style={[
            styles.selectionBar,
            {
              opacity: selectionModeAnimation,
              transform: [{
                translateY: selectionModeAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-50, 0]
                })
              }]
            }
          ]}>
          {isSelectionMode && (
            <>
              <View style={styles.selectionCountContainer}>
                <Ionicons name="checkmark-circle" size={18} color={Colors.AccentBlue} style={{marginRight: 6}} />
                <Typography variant="bodyText" style={styles.selectionText}>
                  {selectedItems.length > 0 ? 
                    `${selectedItems.length} item${selectedItems.length > 1 ? 's' : ''} selected` : 
                    'Select items'}
                </Typography>
              </View>
              <TouchableOpacity 
                style={styles.selectAllButton} 
                onPress={selectAllItems}
                activeOpacity={0.7}
              >
                <Ionicons 
                  name={isAllSelected ? "close-circle-outline" : "checkmark-circle-outline"} 
                  size={18} 
                  color={Colors.AccentBlue} 
                  style={{marginRight: 6}} 
                />
                <Typography
                  variant="bodyText"
                  style={StyleSheet.flatten([
                    styles.selectAllButtonText,
                    isAllSelected && styles.activeSelectAllButtonText,
                  ])}
                >
                  {isAllSelected ? 'Deselect All' : 'Select All'}
                </Typography>
              </TouchableOpacity>
            </>
          )}
          </Animated.View>
        )}
      </View>

      {historyState.isLoading ? (
        <View style={styles.emptyContainer}>
          <Typography variant="bodyText" style={styles.emptyText}>
            Loading history...
          </Typography>
        </View>
      ) : historyState.scans.length > 0 ? (
        <>
          <View style={styles.sectionHeader}>
            <Typography variant="heading2">Your Scans</Typography>
          </View>
          <FlatList
            data={historyState.scans}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyState}
          />
        </>
      ) : (
        renderEmptyState()
      )}

      {/* Action buttons - only show when in selection mode and items are selected */}
      {isSelectionMode && (
        <Animated.View style={[
          styles.actionButtonsContainer,
          {
            opacity: actionButtonsAnimation,
            transform: [{
              translateY: actionButtonsAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [50, 0]
              })
            }]
          }
        ]}>
          <CustomButton
            title={`Delete (${selectedItems.length})`}
            onPress={handleDelete}
            style={StyleSheet.flatten([styles.actionButton, styles.deleteButton])}
            textStyle={styles.deleteButtonText}
            icon={<Ionicons name="trash-outline" size={18} color={Colors.BackgroundPrimary} style={{marginRight: 8}} />}
          />
          <CustomButton
            title={`Compare (${selectedItems.length})`}
            onPress={handleCompare}
            disabled={selectedItems.length < 2 || selectedItems.length > 3}
            style={StyleSheet.flatten([
              styles.actionButton,
              styles.compareButton,
              (selectedItems.length < 2 || selectedItems.length > 3) && styles.disabledButton,
            ])}
            textStyle={(selectedItems.length < 2 || selectedItems.length > 3) ? styles.disabledButtonText : undefined}
            icon={<Ionicons name="git-compare-outline" size={18} color={(selectedItems.length < 2 || selectedItems.length > 3) ? Colors.LightText : Colors.BackgroundPrimary} style={{marginRight: 8}} />}
          />
        </Animated.View>
      )}
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.BackgroundPrimary,
  },
  header: {
    backgroundColor: Colors.BackgroundPrimary,
    paddingTop: Spacing.Medium,
    paddingBottom: Spacing.Small,
    borderBottomWidth: 1,
    borderBottomColor: Colors.SurfaceSecondary,
    ...Shadow.Small,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.Medium,
  },
  title: {
    flex: 1,
    textAlign: 'center',
    color: Colors.DarkText,
  },
  backButton: {
    width: 40,
  },
  selectButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectionBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.Medium,
    paddingTop: Spacing.Small,
    paddingBottom: Spacing.Small,
    borderBottomWidth: 1,
    borderBottomColor: `${Colors.SurfaceSecondary}80`,
  },
  selectionCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectionText: {
    color: Colors.DarkText,
    fontWeight: '500',
  },
  selectAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.ExtraSmall,
    paddingHorizontal: Spacing.Small,
    backgroundColor: `${Colors.AccentBlue}10`,
    borderRadius: BorderRadius.Medium,
  },
  selectAllButtonText: {
    color: Colors.AccentBlue,
    fontWeight: '600',
    fontSize: 14,
  },
  activeSelectAllButtonText: {
    color: Colors.AccentBlue,
  },
  sectionHeader: {
    paddingHorizontal: Spacing.Medium,
    paddingVertical: Spacing.Medium,
  },
  listContent: {
    paddingHorizontal: Spacing.Medium,
    paddingBottom: Spacing.ExtraLarge,
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Spacing.Medium,
    backgroundColor: Colors.BackgroundPrimary,
    borderTopWidth: 1,
    borderTopColor: Colors.SurfaceSecondary,
    ...Shadow.Medium,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: Spacing.ExtraSmall,
    borderRadius: BorderRadius.Medium,
    height: 48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  deleteButton: {
    backgroundColor: Colors.Error,
  },
  deleteButtonText: {
    color: Colors.BackgroundPrimary,
    fontWeight: '600',
  },
  compareButton: {
    backgroundColor: Colors.AccentBlue,
  },
  disabledButton: {
    backgroundColor: `${Colors.SurfaceSecondary}80`,
  },
  disabledButtonText: {
    color: Colors.LightText,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.ExtraLarge,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: `${Colors.AccentBlue}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.Large,
    ...Shadow.Medium,
  },
  emptyTitle: {
    marginBottom: Spacing.Small,
    textAlign: 'center',
    color: Colors.DarkText,
    fontWeight: '600',
  },
  emptySubText: {
    textAlign: 'center',
    color: Colors.LightText,
    maxWidth: '80%',
    lineHeight: 22,
  },
  emptyCta: {
    marginTop: Spacing.ExtraLarge,
    paddingHorizontal: Spacing.Large,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.AccentBlue,
    ...Shadow.Small,
  },
  emptyText: {
    textAlign: 'center',
  },
  itemWrapper: {
    marginBottom: Spacing.Medium,
  },
  listItemCard: {
    borderRadius: BorderRadius.Large,
    overflow: 'hidden',
  },
  selectedCard: {
    borderWidth: 2,
    borderColor: Colors.AccentBlue,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemThumbnail: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.Medium,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Medium,
    marginLeft: Spacing.Medium,
    marginVertical: Spacing.Medium,
    position: 'relative',
  },
  thumbnailText: {
    fontSize: 24,
    fontWeight: '700',
  },
  gradeBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 20,
    height: 20,
    borderRadius: BorderRadius.Round,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1.5,
    borderColor: Colors.BackgroundPrimary,
  },
  gradeText: {
    color: Colors.BackgroundPrimary,
    fontSize: 10,
    fontWeight: '700',
  },
  itemContent: {
    flex: 1,
    paddingVertical: Spacing.Medium,
  },
  itemTitle: {
    color: Colors.DarkText,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metaIcon: {
    marginRight: 4,
  },
  itemSubtitle: {
    color: Colors.LightText,
    fontSize: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tagItem: {
    backgroundColor: Colors.SurfaceSecondary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: BorderRadius.Small,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    color: Colors.LightText,
    fontSize: 10,
  },
  itemRight: {
    marginLeft: Spacing.Medium,
    marginRight: Spacing.Medium,
  },
});

export default HistoryScreen;
